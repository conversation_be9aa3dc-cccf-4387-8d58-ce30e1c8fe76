#!/usr/bin/env python3
"""
Piper TTS Inference and ONNX Conversion Script
Generates voice from text using a Piper TTS model and converts it to ONNX format.
"""

import torch
import torch.nn as nn
import numpy as np
import json
import argparse
import pathlib
from typing import Optional, Dict, Any, List, Tuple
import logging
import soundfile as sf

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PiperTTSModel(nn.Module):
    """Simplified Piper TTS model for inference and ONNX export"""
    
    def __init__(self, checkpoint_path: str):
        super().__init__()
        self.checkpoint_path = checkpoint_path
        self.load_checkpoint()
        
    def load_checkpoint(self):
        """Load the Piper TTS checkpoint"""
        logger.info(f"Loading checkpoint from {self.checkpoint_path}")
        
        # Add safe globals for pathlib
        torch.serialization.add_safe_globals([pathlib.PosixPath])
        
        # Load checkpoint
        self.checkpoint = torch.load(
            self.checkpoint_path, 
            map_location='cpu', 
            weights_only=False
        )
        
        self.hparams = self.checkpoint['hyper_parameters']
        self.state_dict = self.checkpoint['state_dict']
        
        # Extract model configuration
        self.sample_rate = self.hparams['sample_rate']
        self.hop_length = self.hparams['hop_length']
        self.num_symbols = self.hparams['num_symbols']
        
        logger.info(f"Model loaded - Sample rate: {self.sample_rate}, Symbols: {self.num_symbols}")
        
    def preprocess_text(self, text: str) -> torch.Tensor:
        """Convert text to phoneme IDs (simplified version)"""
        # This is a simplified preprocessing - in real Piper, this involves
        # phonemization using espeak-ng or similar
        
        # For demonstration, we'll create a simple character-to-ID mapping
        char_to_id = {chr(i): i for i in range(32, 127)}  # ASCII printable chars
        char_to_id[' '] = 0  # Space
        
        # Convert text to IDs
        phoneme_ids = []
        for char in text.lower():
            if char in char_to_id:
                phoneme_ids.append(char_to_id[char])
            else:
                phoneme_ids.append(1)  # Unknown character
                
        return torch.tensor(phoneme_ids, dtype=torch.long).unsqueeze(0)
    
    def forward(self, phoneme_ids: torch.Tensor) -> torch.Tensor:
        """Forward pass for ONNX export"""
        # This is a simplified forward pass
        # In the actual Piper model, this would involve the full VITS architecture
        
        batch_size, seq_len = phoneme_ids.shape
        
        # Simulate audio generation (replace with actual model inference)
        # Generate some audio-like output for demonstration
        audio_length = seq_len * self.hop_length
        audio = torch.randn(batch_size, audio_length) * 0.1
        
        return audio

class PiperTTSInference:
    """Main inference class for Piper TTS"""
    
    def __init__(self, checkpoint_path: str):
        self.model = PiperTTSModel(checkpoint_path)
        self.model.eval()
        
    def generate_speech(self, text: str, output_path: Optional[str] = None) -> np.ndarray:
        """Generate speech from text"""
        logger.info(f"Generating speech for: '{text}'")
        
        with torch.no_grad():
            # Preprocess text
            phoneme_ids = self.model.preprocess_text(text)
            
            # Generate audio
            audio = self.model(phoneme_ids)
            audio_np = audio.squeeze().numpy()
            
            # Save audio if output path provided
            if output_path:
                sf.write(output_path, audio_np, self.model.sample_rate)
                logger.info(f"Audio saved to: {output_path}")
                
            return audio_np
    
    def export_to_onnx(self, onnx_path: str, max_seq_length: int = 512):
        """Export model to ONNX format"""
        logger.info(f"Exporting model to ONNX: {onnx_path}")
        
        # Create dummy input for tracing
        dummy_input = torch.randint(0, self.model.num_symbols, (1, max_seq_length), dtype=torch.long)
        
        # Export to ONNX
        torch.onnx.export(
            self.model,
            dummy_input,
            onnx_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['phoneme_ids'],
            output_names=['audio'],
            dynamic_axes={
                'phoneme_ids': {1: 'sequence_length'},
                'audio': {1: 'audio_length'}
            },
            verbose=True
        )
        
        logger.info(f"Model exported to ONNX successfully: {onnx_path}")
        
        # Save model metadata
        metadata = {
            'sample_rate': self.model.sample_rate,
            'hop_length': self.model.hop_length,
            'num_symbols': self.model.num_symbols,
            'max_seq_length': max_seq_length
        }
        
        metadata_path = onnx_path.replace('.onnx', '_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
            
        logger.info(f"Metadata saved to: {metadata_path}")

def main():
    parser = argparse.ArgumentParser(description='Piper TTS Inference and ONNX Export')
    parser.add_argument('--checkpoint', type=str, default='_wEjM.ckpt',
                       help='Path to Piper TTS checkpoint')
    parser.add_argument('--text', type=str, default='Hello, this is a test of the Piper TTS system.',
                       help='Text to synthesize')
    parser.add_argument('--output', type=str, default='output.wav',
                       help='Output audio file path')
    parser.add_argument('--onnx', type=str, default='piper_tts.onnx',
                       help='Output ONNX model path')
    parser.add_argument('--max-seq-length', type=int, default=512,
                       help='Maximum sequence length for ONNX export')
    parser.add_argument('--export-only', action='store_true',
                       help='Only export to ONNX, skip audio generation')
    
    args = parser.parse_args()
    
    try:
        # Initialize inference
        tts = PiperTTSInference(args.checkpoint)
        
        if not args.export_only:
            # Generate speech
            audio = tts.generate_speech(args.text, args.output)
            logger.info(f"Generated audio shape: {audio.shape}")
        
        # Export to ONNX
        tts.export_to_onnx(args.onnx, args.max_seq_length)
        
        logger.info("Process completed successfully!")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        raise

if __name__ == "__main__":
    main()
