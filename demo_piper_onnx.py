#!/usr/bin/env python3
"""
Demo: Piper TTS ONNX Model Usage
Demonstrates how to use the converted ONNX model for text-to-speech synthesis
"""

import onnxruntime as ort
import numpy as np
import json
import soundfile as sf
import argparse
import time
from typing import List

def load_model(onnx_path: str, metadata_path: str):
    """Load ONNX model and metadata"""
    print(f"Loading ONNX model: {onnx_path}")
    
    # Load metadata
    with open(metadata_path, 'r') as f:
        metadata = json.load(f)
    
    # Create ONNX session
    session = ort.InferenceSession(onnx_path)
    
    return session, metadata

def text_to_phoneme_ids(text: str, num_symbols: int, max_length: int = 128) -> np.ndarray:
    """Convert text to phoneme IDs (simplified)"""
    phoneme_ids = []
    
    for char in text.lower():
        if char.isalpha():
            # Map a-z to 1-26
            phoneme_ids.append(ord(char) - ord('a') + 1)
        elif char.isspace():
            phoneme_ids.append(0)  # Space
        elif char.isdigit():
            # Map 0-9 to 27-36
            phoneme_ids.append(ord(char) - ord('0') + 27)
        elif char in '.,!?;:':
            # Punctuation
            phoneme_ids.append(37 + ord(char) % 10)
        else:
            # Other characters
            phoneme_ids.append(min(ord(char) % num_symbols, num_symbols - 1))
    
    # Pad or truncate
    if len(phoneme_ids) > max_length:
        phoneme_ids = phoneme_ids[:max_length]
    else:
        phoneme_ids.extend([0] * (max_length - len(phoneme_ids)))
    
    return np.array([phoneme_ids], dtype=np.int64)

def synthesize_speech(session, metadata: dict, text: str) -> np.ndarray:
    """Synthesize speech from text"""
    print(f"Synthesizing: '{text}'")
    
    # Convert text to phoneme IDs
    phoneme_ids = text_to_phoneme_ids(
        text, 
        metadata['num_symbols'], 
        metadata['max_seq_length']
    )
    
    # Run inference
    start_time = time.time()
    outputs = session.run(['audio'], {'phoneme_ids': phoneme_ids})
    inference_time = time.time() - start_time
    
    audio = outputs[0].squeeze()
    
    print(f"✓ Generated {len(audio)} samples in {inference_time:.3f}s")
    print(f"✓ Audio duration: {len(audio) / metadata['sample_rate']:.2f}s")
    
    return audio

def demo_batch_synthesis(session, metadata: dict, texts: List[str]):
    """Demo batch synthesis"""
    print("\n" + "="*60)
    print("BATCH SYNTHESIS DEMO")
    print("="*60)
    
    for i, text in enumerate(texts):
        print(f"\n[{i+1}/{len(texts)}] Processing...")
        
        audio = synthesize_speech(session, metadata, text)
        
        # Save audio
        output_file = f"demo_output_{i+1}.wav"
        sf.write(output_file, audio, metadata['sample_rate'])
        print(f"✓ Saved: {output_file}")

def demo_interactive(session, metadata: dict):
    """Interactive demo"""
    print("\n" + "="*60)
    print("INTERACTIVE PIPER TTS ONNX DEMO")
    print("="*60)
    print("Enter text to synthesize (or 'quit' to exit)")
    print("Examples:")
    print("- Hello world")
    print("- This is a test of Piper TTS")
    print("- The quick brown fox jumps over the lazy dog")
    
    counter = 1
    while True:
        try:
            text = input(f"\n[{counter}] > ").strip()
            
            if text.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not text:
                continue
            
            # Synthesize
            audio = synthesize_speech(session, metadata, text)
            
            # Save
            output_file = f"interactive_{counter}.wav"
            sf.write(output_file, audio, metadata['sample_rate'])
            print(f"✓ Saved: {output_file}")
            
            counter += 1
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    parser = argparse.ArgumentParser(description='Piper TTS ONNX Demo')
    parser.add_argument('--model', type=str, default='piper_tts_simple.onnx',
                       help='ONNX model path')
    parser.add_argument('--metadata', type=str, default='piper_tts_simple_metadata.json',
                       help='Metadata JSON path')
    parser.add_argument('--mode', type=str, choices=['single', 'batch', 'interactive'], 
                       default='interactive', help='Demo mode')
    parser.add_argument('--text', type=str, default='Hello world from Piper TTS ONNX',
                       help='Text for single mode')
    parser.add_argument('--output', type=str, default='demo_output.wav',
                       help='Output file for single mode')
    
    args = parser.parse_args()
    
    try:
        # Load model
        session, metadata = load_model(args.model, args.metadata)
        
        print("="*60)
        print("PIPER TTS ONNX MODEL LOADED")
        print("="*60)
        print(f"Model: {args.model}")
        print(f"Sample Rate: {metadata['sample_rate']} Hz")
        print(f"Symbols: {metadata['num_symbols']}")
        print(f"Max Sequence Length: {metadata['max_seq_length']}")
        
        if args.mode == 'single':
            # Single synthesis
            audio = synthesize_speech(session, metadata, args.text)
            sf.write(args.output, audio, metadata['sample_rate'])
            print(f"\n✓ Audio saved to: {args.output}")
            
        elif args.mode == 'batch':
            # Batch synthesis
            demo_texts = [
                "Hello world from Piper TTS",
                "This is a demonstration of neural text to speech",
                "The quick brown fox jumps over the lazy dog",
                "ONNX makes it easy to deploy machine learning models",
                "Piper TTS provides high quality voice synthesis"
            ]
            demo_batch_synthesis(session, metadata, demo_texts)
            
        elif args.mode == 'interactive':
            # Interactive mode
            demo_interactive(session, metadata)
        
        print("\n" + "="*60)
        print("DEMO COMPLETED!")
        print("="*60)
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise

if __name__ == "__main__":
    main()
