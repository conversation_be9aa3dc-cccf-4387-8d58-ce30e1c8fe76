#!/usr/bin/env python3
"""
ONNX Inference Example for Piper TTS
Demonstrates how to use the converted ONNX model for text-to-speech inference
"""

import onnxruntime as ort
import numpy as np
import json
import argparse
import soundfile as sf
import logging
from typing import Dict, Any, List

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PiperONNXInference:
    """ONNX inference class for Piper TTS"""
    
    def __init__(self, onnx_path: str, metadata_path: str = None):
        self.onnx_path = onnx_path
        self.metadata_path = metadata_path or onnx_path.replace('.onnx', '_metadata.json')
        
        # Load metadata
        self.load_metadata()
        
        # Initialize ONNX runtime session
        self.init_onnx_session()
        
    def load_metadata(self):
        """Load model metadata"""
        try:
            with open(self.metadata_path, 'r') as f:
                self.metadata = json.load(f)
            
            self.sample_rate = self.metadata['sample_rate']
            self.num_symbols = self.metadata['num_symbols']
            self.max_seq_length = self.metadata['max_seq_length']
            
            logger.info(f"Loaded metadata - Sample rate: {self.sample_rate}, Symbols: {self.num_symbols}")
            
        except FileNotFoundError:
            logger.warning(f"Metadata file not found: {self.metadata_path}")
            # Use default values
            self.metadata = {}
            self.sample_rate = 22050
            self.num_symbols = 256
            self.max_seq_length = 256
            
    def init_onnx_session(self):
        """Initialize ONNX runtime session"""
        logger.info(f"Loading ONNX model: {self.onnx_path}")
        
        # Configure ONNX runtime session
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        # Create session
        self.session = ort.InferenceSession(self.onnx_path, sess_options)
        
        # Get input/output info
        self.input_names = [input.name for input in self.session.get_inputs()]
        self.output_names = [output.name for output in self.session.get_outputs()]
        
        logger.info(f"Model loaded - Inputs: {self.input_names}, Outputs: {self.output_names}")
        
    def text_to_phoneme_ids(self, text: str) -> np.ndarray:
        """Convert text to phoneme IDs (simplified)"""
        # This is a very basic conversion - in real Piper, this would use
        # proper phonemization with espeak-ng or similar
        
        # Simple character-based conversion
        phoneme_ids = []
        for char in text.lower():
            if char.isalpha():
                # Map a-z to 1-26
                phoneme_ids.append(ord(char) - ord('a') + 1)
            elif char.isspace():
                phoneme_ids.append(0)  # Space
            elif char.isdigit():
                # Map 0-9 to 27-36
                phoneme_ids.append(ord(char) - ord('0') + 27)
            else:
                # Other characters
                phoneme_ids.append(min(ord(char) % self.num_symbols, self.num_symbols - 1))
        
        # Pad or truncate to max length
        if len(phoneme_ids) > self.max_seq_length:
            phoneme_ids = phoneme_ids[:self.max_seq_length]
        else:
            phoneme_ids.extend([0] * (self.max_seq_length - len(phoneme_ids)))
        
        return np.array([phoneme_ids], dtype=np.int64)
    
    def synthesize(self, text: str) -> np.ndarray:
        """Synthesize speech from text"""
        logger.info(f"Synthesizing: '{text}'")
        
        # Convert text to phoneme IDs
        phoneme_ids = self.text_to_phoneme_ids(text)
        
        # Run inference
        inputs = {self.input_names[0]: phoneme_ids}
        outputs = self.session.run(self.output_names, inputs)
        
        # Get audio output
        audio = outputs[0].squeeze()
        
        logger.info(f"Generated audio shape: {audio.shape}")
        return audio
    
    def synthesize_to_file(self, text: str, output_path: str):
        """Synthesize speech and save to file"""
        audio = self.synthesize(text)
        
        # Normalize audio
        audio = np.clip(audio, -1.0, 1.0)
        
        # Save to file
        sf.write(output_path, audio, self.sample_rate)
        logger.info(f"Audio saved to: {output_path}")
        
        return audio

def main():
    parser = argparse.ArgumentParser(description='ONNX Inference for Piper TTS')
    parser.add_argument('--model', type=str, default='piper_tts.onnx',
                       help='Path to ONNX model')
    parser.add_argument('--metadata', type=str, default=None,
                       help='Path to metadata JSON file')
    parser.add_argument('--text', type=str, 
                       default='Hello, this is a test of the ONNX Piper TTS model.',
                       help='Text to synthesize')
    parser.add_argument('--output', type=str, default='output.wav',
                       help='Output audio file')
    parser.add_argument('--interactive', action='store_true',
                       help='Interactive mode - enter text to synthesize')
    
    args = parser.parse_args()
    
    try:
        # Initialize inference
        tts = PiperONNXInference(args.model, args.metadata)
        
        if args.interactive:
            # Interactive mode
            print("\n" + "="*50)
            print("INTERACTIVE PIPER TTS ONNX INFERENCE")
            print("="*50)
            print("Enter text to synthesize (or 'quit' to exit):")
            
            while True:
                text = input("\n> ").strip()
                if text.lower() in ['quit', 'exit', 'q']:
                    break
                
                if text:
                    try:
                        output_file = f"interactive_{len(text)}.wav"
                        tts.synthesize_to_file(text, output_file)
                        print(f"✓ Audio saved to: {output_file}")
                    except Exception as e:
                        print(f"✗ Error: {e}")
        else:
            # Single synthesis
            tts.synthesize_to_file(args.text, args.output)
            
            print("\n" + "="*50)
            print("SYNTHESIS COMPLETED!")
            print("="*50)
            print(f"Text: {args.text}")
            print(f"Output: {args.output}")
            print(f"Sample Rate: {tts.sample_rate} Hz")
            print("="*50)
        
    except Exception as e:
        logger.error(f"Inference failed: {e}")
        raise

if __name__ == "__main__":
    main()
