#!/usr/bin/env python3
"""
Setup and Run Script for Piper TTS ONNX Conversion
Installs dependencies and runs the conversion process
"""

import subprocess
import sys
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_dependencies():
    """Install required dependencies"""
    logger.info("Installing dependencies...")
    
    dependencies = [
        "torch>=1.13.0",
        "torchaudio>=0.13.0", 
        "numpy>=1.21.0",
        "soundfile>=0.10.0",
        "onnx>=1.12.0",
        "onnxruntime>=1.12.0",
        "pytorch-lightning>=1.8.0",
        "librosa>=0.9.0",
        "scipy>=1.7.0"
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            logger.info(f"✓ Installed {dep}")
        except subprocess.CalledProcessError as e:
            logger.error(f"✗ Failed to install {dep}: {e}")
            return False
    
    return True

def run_conversion():
    """Run the ONNX conversion"""
    logger.info("Running Piper TTS to ONNX conversion...")
    
    try:
        # Run the conversion script
        result = subprocess.run([
            sys.executable, "piper_onnx_converter.py",
            "--checkpoint", "_wEjM.ckpt",
            "--output", "piper_tts.onnx",
            "--max-seq-length", "256",
            "--test-text", "Hello, this is a test of Piper TTS conversion to ONNX format."
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ Conversion completed successfully!")
            print(result.stdout)
        else:
            logger.error("✗ Conversion failed!")
            print(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"✗ Error running conversion: {e}")
        return False
    
    return True

def verify_files():
    """Verify that output files were created"""
    logger.info("Verifying output files...")
    
    expected_files = [
        "piper_tts.onnx",
        "piper_tts_metadata.json"
    ]
    
    all_exist = True
    for file in expected_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            logger.info(f"✓ {file} ({size:,} bytes)")
        else:
            logger.error(f"✗ {file} not found")
            all_exist = False
    
    return all_exist

def main():
    """Main setup and run function"""
    print("="*60)
    print("PIPER TTS TO ONNX CONVERTER - SETUP AND RUN")
    print("="*60)
    
    # Step 1: Install dependencies
    print("\n1. Installing dependencies...")
    if not install_dependencies():
        print("❌ Dependency installation failed!")
        return 1
    
    # Step 2: Run conversion
    print("\n2. Running conversion...")
    if not run_conversion():
        print("❌ Conversion failed!")
        return 1
    
    # Step 3: Verify files
    print("\n3. Verifying output files...")
    if not verify_files():
        print("❌ File verification failed!")
        return 1
    
    print("\n" + "="*60)
    print("🎉 SUCCESS! Piper TTS model converted to ONNX format")
    print("="*60)
    print("\nOutput files:")
    print("- piper_tts.onnx (ONNX model)")
    print("- piper_tts_metadata.json (Model metadata)")
    print("\nYou can now use the ONNX model for inference!")
    print("="*60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
