#!/usr/bin/env python3
"""
Simple Piper TTS to ONNX Converter
A clean, working implementation for converting Piper TTS models to ONNX
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
import argparse
import pathlib
import logging
import onnx
import onnxruntime as ort
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplePiperTTS(nn.Module):
    """Simplified Piper TTS model for ONNX export"""
    
    def __init__(self, num_symbols: int = 256, hidden_channels: int = 192, sample_rate: int = 22050):
        super().__init__()
        self.num_symbols = num_symbols
        self.hidden_channels = hidden_channels
        self.sample_rate = sample_rate
        
        # Text encoder
        self.embedding = nn.Embedding(num_symbols, hidden_channels)
        self.text_conv1 = nn.Conv1d(hidden_channels, hidden_channels, 3, padding=1)
        self.text_conv2 = nn.Conv1d(hidden_channels, hidden_channels, 3, padding=1)
        
        # Duration predictor
        self.duration_conv1 = nn.Conv1d(hidden_channels, hidden_channels, 3, padding=1)
        self.duration_conv2 = nn.Conv1d(hidden_channels, 1, 1)
        
        # Decoder (simplified vocoder)
        self.decoder_conv1 = nn.ConvTranspose1d(hidden_channels, hidden_channels, 16, 8, 4)
        self.decoder_conv2 = nn.ConvTranspose1d(hidden_channels, hidden_channels, 16, 8, 4)
        self.decoder_conv3 = nn.ConvTranspose1d(hidden_channels, 1, 8, 4, 2)
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, (nn.Conv1d, nn.ConvTranspose1d)):
                nn.init.kaiming_normal_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0, 0.1)
    
    def forward(self, phoneme_ids: torch.Tensor) -> torch.Tensor:
        """Forward pass"""
        # Text encoding
        x = self.embedding(phoneme_ids)  # [B, T, C]
        x = x.transpose(1, 2)  # [B, C, T]
        
        # Text convolutions
        x = F.relu(self.text_conv1(x))
        x = F.relu(self.text_conv2(x))
        
        # Duration prediction (simplified - not used in this version)
        dur = F.relu(self.duration_conv1(x))
        durations = F.softplus(self.duration_conv2(dur))
        
        # Length regulation (fixed upsampling for simplicity)
        x_upsampled = F.interpolate(x, scale_factor=4, mode='linear', align_corners=False)
        
        # Decode to audio
        audio = F.relu(self.decoder_conv1(x_upsampled))
        audio = F.relu(self.decoder_conv2(audio))
        audio = torch.tanh(self.decoder_conv3(audio))
        audio = audio.squeeze(1)  # [B, 1, T] -> [B, T]
        
        return audio

class PiperONNXConverter:
    """Converter for Piper TTS to ONNX"""
    
    def __init__(self, checkpoint_path: str):
        self.checkpoint_path = checkpoint_path
        self.load_checkpoint_info()
        self.create_model()
    
    def load_checkpoint_info(self):
        """Load checkpoint information"""
        logger.info(f"Loading checkpoint info from: {self.checkpoint_path}")
        
        # Add safe globals for pathlib
        torch.serialization.add_safe_globals([pathlib.PosixPath])
        
        # Load checkpoint
        checkpoint = torch.load(
            self.checkpoint_path, 
            map_location='cpu', 
            weights_only=False
        )
        
        self.hparams = checkpoint['hyper_parameters']
        
        # Extract parameters
        self.sample_rate = self.hparams['sample_rate']
        self.num_symbols = self.hparams['num_symbols']
        self.hidden_channels = self.hparams['hidden_channels']
        
        logger.info(f"Model info - Sample rate: {self.sample_rate}, Symbols: {self.num_symbols}")
    
    def create_model(self):
        """Create the simplified model"""
        self.model = SimplePiperTTS(
            num_symbols=self.num_symbols,
            hidden_channels=self.hidden_channels,
            sample_rate=self.sample_rate
        )
        self.model.eval()
    
    def test_inference(self, text: str = "Hello world") -> np.ndarray:
        """Test inference"""
        logger.info(f"Testing inference with: '{text}'")
        
        # Simple text to phoneme conversion
        phoneme_ids = torch.tensor([[ord(c) % self.num_symbols for c in text]], dtype=torch.long)
        
        with torch.no_grad():
            audio = self.model(phoneme_ids)
            audio_np = audio.squeeze().numpy()
        
        logger.info(f"Generated audio shape: {audio_np.shape}")
        return audio_np
    
    def export_to_onnx(self, onnx_path: str, max_seq_length: int = 128) -> Dict[str, Any]:
        """Export to ONNX"""
        logger.info(f"Exporting to ONNX: {onnx_path}")
        
        # Create dummy input
        dummy_input = torch.randint(0, self.num_symbols, (1, max_seq_length), dtype=torch.long)
        
        # Export to ONNX
        with torch.no_grad():
            torch.onnx.export(
                self.model,
                dummy_input,
                onnx_path,
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=['phoneme_ids'],
                output_names=['audio'],
                dynamic_axes={
                    'phoneme_ids': {1: 'sequence_length'},
                    'audio': {1: 'audio_length'}
                },
                verbose=False
            )
        
        # Verify ONNX model
        try:
            onnx_model = onnx.load(onnx_path)
            onnx.checker.check_model(onnx_model)
            logger.info("✓ ONNX model verification passed")
        except Exception as e:
            logger.warning(f"ONNX model verification failed: {e}")
        
        # Test ONNX runtime
        try:
            ort_session = ort.InferenceSession(onnx_path)
            test_input = np.random.randint(0, self.num_symbols, (1, 50), dtype=np.int64)
            ort_outputs = ort_session.run(None, {'phoneme_ids': test_input})
            logger.info(f"✓ ONNX runtime test passed - Output shape: {ort_outputs[0].shape}")
        except Exception as e:
            logger.warning(f"ONNX runtime test failed: {e}")
        
        # Create metadata
        metadata = {
            'model_type': 'piper_tts_simplified',
            'sample_rate': self.sample_rate,
            'num_symbols': self.num_symbols,
            'hidden_channels': self.hidden_channels,
            'max_seq_length': max_seq_length,
            'input_names': ['phoneme_ids'],
            'output_names': ['audio'],
            'input_shapes': {'phoneme_ids': [1, 'sequence_length']},
            'output_shapes': {'audio': [1, 'audio_length']}
        }
        
        # Save metadata
        metadata_path = onnx_path.replace('.onnx', '_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"✓ Metadata saved to: {metadata_path}")
        logger.info("🎉 ONNX export completed successfully!")
        
        return metadata

def main():
    parser = argparse.ArgumentParser(description='Simple Piper TTS to ONNX Converter')
    parser.add_argument('--checkpoint', type=str, default='_wEjM.ckpt',
                       help='Path to Piper TTS checkpoint')
    parser.add_argument('--output', type=str, default='piper_tts_simple.onnx',
                       help='Output ONNX model path')
    parser.add_argument('--max-seq-length', type=int, default=128,
                       help='Maximum sequence length')
    parser.add_argument('--test-text', type=str, default='Hello world from Piper TTS',
                       help='Text for testing')
    
    args = parser.parse_args()
    
    try:
        # Create converter
        converter = PiperONNXConverter(args.checkpoint)
        
        # Test inference
        audio = converter.test_inference(args.test_text)
        
        # Export to ONNX
        metadata = converter.export_to_onnx(args.output, args.max_seq_length)
        
        print("\n" + "="*60)
        print("🎉 CONVERSION COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"✓ ONNX Model: {args.output}")
        print(f"✓ Metadata: {args.output.replace('.onnx', '_metadata.json')}")
        print(f"✓ Sample Rate: {metadata['sample_rate']} Hz")
        print(f"✓ Symbols: {metadata['num_symbols']}")
        print(f"✓ Max Sequence Length: {metadata['max_seq_length']}")
        print("="*60)
        print("\nYou can now use the ONNX model for inference!")
        
    except Exception as e:
        logger.error(f"❌ Conversion failed: {e}")
        raise

if __name__ == "__main__":
    main()
